/* ... keep existing code (all previous styles) the same ... */

/* Brand accent on page headers */
.page-header h1 {
  position: relative;
  padding-left: 16px;
  display: flex;
  align-items: center;
}

.page-header h1::before {
  content: '';
  width: 4px;
  height: 32px;
  background: #DC2626;
  display: inline-block;
  margin-right: 12px;
  vertical-align: middle;
  border-radius: 2px;
}

/* Enhanced brand presence for main content headers */
.brand-header {
  position: relative;
  padding-left: 16px;
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.brand-header::before {
  content: '';
  width: 4px;
  height: 32px;
  background: linear-gradient(180deg, #DC2626, #3B82F6);
  display: inline-block;
  margin-right: 12px;
  border-radius: 2px;
}

.brand-header h1, .brand-header h2 {
  margin: 0;
  color: #FFFFFF !important;
  font-weight: 600;
}

/* Brand button utility classes - apply these to specific buttons that need brand colors */
.btn-brand {
  background: #DC2626 !important;
  border: 1px solid #DC2626 !important;
  color: #FFFFFF !important;
}

.btn-brand:hover {
  background: #991B1B !important;
  border-color: #991B1B !important;
}

/* Brand outline button utility classes */
.btn-brand-outline,
.disconnect-button {
  background: transparent !important;
  border: 1px solid #3B82F6 !important;
  color: #3B82F6 !important;
}

.btn-brand-outline:hover,
.disconnect-button:hover {
  background: #3B82F6 !important;
  border-color: #3B82F6 !important;
  color: #FFFFFF !important;
}

/* Ghost buttons */
button[variant="ghost"] {
  background: transparent !important;
  border: none !important;
  color: #FFFFFF !important;
}

button[variant="ghost"]:hover {
  background: #4B5563 !important;
  color: #FFFFFF !important;
}

/* Link buttons */
button[variant="link"] {
  background: transparent !important;
  border: none !important;
  color: #DC2626 !important;
  text-decoration: underline;
}

button[variant="link"]:hover {
  color: #991B1B !important;
}

/* All select dropdowns */
select,
.form-select,
.custom-select,
.pillar-dropdown {
  background: #374151 !important;
  border: 1px solid #4B5563 !important;
  color: #FFFFFF !important;
}

/* Date inputs */
input[type="date"],
.date-input {
  background: #374151 !important;
  border: 1px solid #4B5563 !important;
  color: #FFFFFF !important;
}

/* Any remaining form controls */
.form-control {
  background: #374151 !important;
  border: 1px solid #4B5563 !important;
  color: #FFFFFF !important;
}

/* Focus states for all inputs */
input:focus,
select:focus,
textarea:focus {
  border-color: #3B82F6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
  outline: none !important;
}

/* Placeholder text */
::placeholder {
  color: #9CA3AF !important;
}

/* Override any remaining white backgrounds in buttons */
button.text-red-600,
.text-red-600 {
  color: #FFFFFF !important;
}

/* Ensure dropdown menus have proper black styling */
.bg-popover,
[data-radix-popper-content-wrapper] {
  background: #000000 !important;
  border: 1px solid #333333 !important;
  color: #FFFFFF !important;
}

/* Select content styling */
[role="listbox"],
[role="option"] {
  background: #000000 !important;
  color: #FFFFFF !important;
}

[role="option"]:hover {
  background: #333333 !important;
}
