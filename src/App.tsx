import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';
import { AuthProvider } from '@/components/AuthProvider';
import { Toaster } from '@/components/ui/sonner';
import { SidebarProvider } from '@/components/ui/sidebar';
import AppContent from '@/components/AppContent';
import AuthPage from '@/pages/AuthPage';
import DashboardPage from '@/pages/DashboardPage';
import PillarsPage from '@/pages/PillarsPage';
import IdeasPage from '@/pages/IdeasPage';
import CreatorStudioPage from '@/pages/CreatorStudioPage';
import CalendarPage from '@/pages/CalendarPage';

// GoalsPage removed - goals are now integrated into Dashboard
import SettingsPage from '@/pages/SettingsPage';
import BillingPage from '@/pages/BillingPage';
import BillingSuccessPage from '@/pages/BillingSuccessPage';
import BillingCancelPage from '@/pages/BillingCancelPage';
import PricingPage from '@/pages/PricingPage';
import YouTubeCallbackPage from '@/pages/YouTubeCallbackPage';
import NotFound from '@/pages/NotFound';
import PrivacyPage from '@/pages/PrivacyPage';
import TermsPage from '@/pages/TermsPage';
import SupportPage from '@/pages/SupportPage';
import AdminCleanup from '@/pages/AdminCleanup';
import TestPage from '@/pages/TestPage';

import TestVideoService from './TestVideoService';
import { useEffect, ErrorInfo, Component } from 'react';
import { checkSupabaseConnection } from '@/lib/supabase';
import { toast } from 'sonner';
import './App.css';

// Error Boundary Component
class ErrorBoundary extends Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen w-full bg-red-900 flex items-center justify-center">
          <div className="text-white text-center p-8">
            <h1 className="text-2xl font-bold mb-4">Something went wrong</h1>
            <p className="mb-4">The application encountered an error:</p>
            <pre className="bg-black p-4 rounded text-sm overflow-auto max-w-2xl">
              {this.state.error?.message}
            </pre>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Create a client with enhanced security settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        return failureCount < 3;
      },
    },
  },
});

function App() {
  // Set security headers and CSP - temporarily disabled for debugging
  useEffect(() => {
    // TODO: Re-enable CSP after fixing black screen issue
    console.log('App component mounted');
    console.log('Current location:', window.location.pathname);
  }, []);

  // Debug mode removed - app should work normally now

  useEffect(() => {
    // Check Supabase connection on app start
    const checkConnection = async () => {
      const result = await checkSupabaseConnection();
      console.log('Supabase connection check result:', result);
      
      if (!result.ok) {
        toast.error('Database connection issue detected');
      }
    };
    
    checkConnection();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false}>
          <AuthProvider>
            <SidebarProvider>
              <Router>
                <div className="App min-h-screen w-full bg-black">
                  <Routes>
                    {/* Public routes */}
                    <Route path="/auth" element={<AuthPage />} />
                    <Route path="/privacy" element={<PrivacyPage />} />
                    <Route path="/terms" element={<TermsPage />} />
                    <Route path="/support" element={<SupportPage />} />
                    <Route path="/pricing" element={<PricingPage />} />
                    <Route path="/billing-success" element={<BillingSuccessPage />} />
                    <Route path="/billing-cancel" element={<BillingCancelPage />} />
                    <Route path="/auth/youtube/callback" element={<YouTubeCallbackPage />} />
                    <Route path="/test-video-service" element={<TestVideoService />} />
                    <Route path="/test" element={<TestPage />} />

                    {/* Protected routes wrapped in AppContent */}
                    <Route path="/" element={<AppContent />}>
                      <Route index element={<Navigate to="/dashboard" replace />} />
                      <Route path="dashboard" element={<DashboardPage />} />
                      <Route path="pillars" element={<PillarsPage />} />
                      <Route path="ideas" element={<IdeasPage />} />
                      <Route path="creator-studio" element={<CreatorStudioPage />} />
                      <Route path="calendar" element={<CalendarPage />} />
                      {/* Goals route removed - goals are now integrated into Dashboard */}
                      <Route path="settings" element={<SettingsPage />} />
                      <Route path="billing" element={<BillingPage />} />
                      <Route path="admin-cleanup" element={<AdminCleanup />} />
                    </Route>

                    <Route path="*" element={<NotFound />} />
                  </Routes>
                  <Toaster />
                </div>
              </Router>
            </SidebarProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
