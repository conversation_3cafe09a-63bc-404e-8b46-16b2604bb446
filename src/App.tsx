import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';
import { AuthProvider } from '@/components/AuthProvider';
import { Toaster } from '@/components/ui/sonner';
import { SidebarProvider } from '@/components/ui/sidebar';
import AppContent from '@/components/AppContent';
import AuthPage from '@/pages/AuthPage';
import DashboardPage from '@/pages/DashboardPage';
import PillarsPage from '@/pages/PillarsPage';
import IdeasPage from '@/pages/IdeasPage';
import CreatorStudioPage from '@/pages/CreatorStudioPage';
import CalendarPage from '@/pages/CalendarPage';

// GoalsPage removed - goals are now integrated into Dashboard
import SettingsPage from '@/pages/SettingsPage';
import BillingPage from '@/pages/BillingPage';
import BillingSuccessPage from '@/pages/BillingSuccessPage';
import BillingCancelPage from '@/pages/BillingCancelPage';
import PricingPage from '@/pages/PricingPage';
import YouTubeCallbackPage from '@/pages/YouTubeCallbackPage';
import NotFound from '@/pages/NotFound';
import PrivacyPage from '@/pages/PrivacyPage';
import TermsPage from '@/pages/TermsPage';
import SupportPage from '@/pages/SupportPage';
import AdminCleanup from '@/pages/AdminCleanup';
import TestPage from '@/pages/TestPage';

import TestVideoService from './TestVideoService';
import { useEffect } from 'react';
import { checkSupabaseConnection } from '@/lib/supabase';
import { toast } from 'sonner';
import ErrorBoundary from '@/components/ErrorBoundary';
import './App.css';

// Create a client with enhanced security settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        return failureCount < 3;
      },
    },
  },
});

function App() {
  // Set security headers and CSP - temporarily disabled for debugging
  useEffect(() => {
    // TODO: Re-enable CSP after fixing black screen issue
    console.log('App component mounted');
    console.log('Current location:', window.location.pathname);
  }, []);

  // Debug mode removed - app should work normally now

  useEffect(() => {
    // Check Supabase connection on app start
    const checkConnection = async () => {
      console.log('Checking Supabase connection...');
      const result = await checkSupabaseConnection();
      console.log('Supabase connection check result:', result);
      
      if (!result.ok) {
        console.error('Supabase connection failed:', result.error);
        toast.error('Database connection issue detected', {
          description: result.error?.message || 'An unknown error occurred.',
        });
      } else {
        console.log('Supabase connection successful.');
      }
    };
    
    checkConnection();
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false}>
          <AuthProvider>
            <SidebarProvider>
              <Router>
                <div className="App min-h-screen w-full bg-black">
                  <Routes>
                    {/* Public routes */}
                    <Route path="/auth" element={<AuthPage />} />
                    <Route path="/privacy" element={<PrivacyPage />} />
                    <Route path="/terms" element={<TermsPage />} />
                    <Route path="/support" element={<SupportPage />} />
                    <Route path="/pricing" element={<PricingPage />} />
                    <Route path="/billing-success" element={<BillingSuccessPage />} />
                    <Route path="/billing-cancel" element={<BillingCancelPage />} />
                    <Route path="/auth/youtube/callback" element={<YouTubeCallbackPage />} />
                    <Route path="/test-video-service" element={<TestVideoService />} />
                    <Route path="/test" element={<TestPage />} />

                    {/* Protected routes wrapped in AppContent */}
                    <Route path="/" element={<AppContent />}>
                      <Route index element={<Navigate to="/dashboard" replace />} />
                      <Route path="dashboard" element={<DashboardPage />} />
                      <Route path="pillars" element={<PillarsPage />} />
                      <Route path="ideas" element={<IdeasPage />} />
                      <Route path="creator-studio" element={<CreatorStudioPage />} />
                      <Route path="calendar" element={<CalendarPage />} />
                      {/* Goals route removed - goals are now integrated into Dashboard */}
                      <Route path="settings" element={<SettingsPage />} />
                      <Route path="billing" element={<BillingPage />} />
                      <Route path="admin-cleanup" element={<AdminCleanup />} />
                    </Route>

                    <Route path="*" element={<NotFound />} />
                  </Routes>
                  <Toaster />
                </div>
              </Router>
            </SidebarProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
