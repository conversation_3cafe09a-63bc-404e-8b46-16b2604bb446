
import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { useAuthContext } from '@/components/AuthProvider';
import AuthPage from '@/pages/AuthPage';
import { AppSidebar } from '@/components/AppSidebar';
import Footer from '@/components/Layout/Footer';
import { SidebarInset } from '@/components/ui/sidebar';

const AppContent = () => {
  const { user } = useAuthContext();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log('AppContent: useEffect triggered, user:', !!user);
    // Simulate checking auth state
    const timer = setTimeout(() => {
      console.log('AppContent: Setting loading to false');
      setIsLoading(false);
    }, 300);
    return () => clearTimeout(timer);
  }, [user]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary rounded-full border-t-transparent"></div>
      </div>
    );
  }

  // If user is not authenticated, show auth page
  if (!user) {
    console.log('AppContent: No user found, showing auth page');
    return (
      <div className="min-h-screen flex flex-col w-full">
        <AuthPage />
        <Footer />
      </div>
    );
  }

  console.log('AppContent: User authenticated, showing main app');
  // Authenticated user interface with sidebar and main content
  return (
    <div className="min-h-screen flex flex-row w-full h-screen overflow-hidden">
      <div className="fixed top-0 left-0 z-50 h-screen">
        <AppSidebar />
      </div>
      <SidebarInset className="flex-1 ml-[calc(var(--sidebar-width)+6rem)] mr-12 h-screen rounded-2xl overflow-hidden">
        <main className="flex-1 overflow-auto h-full w-full scrollbar-hide">
          <Outlet />
        </main>
      </SidebarInset>
    </div>
  );
};

export default AppContent;
