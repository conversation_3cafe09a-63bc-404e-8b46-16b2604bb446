import React, { useState, useEffect } from 'react';
import ConnectionError from './ConnectionError';
import { supabase } from '@/lib/supabase-client';

interface ConnectionErrorBoundaryProps {
  children: React.ReactNode;
  fallbackComponent?: React.ReactNode;
  errorType?: 'server' | 'database' | 'api';
}

const ConnectionErrorBoundary = ({ 
  children, 
  fallbackComponent, 
  errorType = 'server' 
}: ConnectionErrorBoundaryProps) => {
  const [isConnected, setIsConnected] = useState(true);
  const [isChecking, setIsChecking] = useState(true);
  const [retryCount, setRetryCount] = useState(0);

  const checkConnection = async () => {
    setIsChecking(true);
    try {
      // For database connection check
      if (errorType === 'database') {
        const { data, error } = await supabase
          .from('users')
          .select('count', { count: 'exact', head: true });
          
        if (error) throw error;
        setIsConnected(true);
      } 
      // For API connection check
      else if (errorType === 'api') {
        const response = await fetch('/api/health', { 
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        
        if (!response.ok) throw new Error('API health check failed');
        setIsConnected(true);
      }
      // For general server connection
      else {
        const response = await fetch('/health', { 
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });
        
        if (!response.ok) throw new Error('Server health check failed');
        setIsConnected(true);
      }
    } catch (error) {
      console.error('Connection check failed:', error);
      setIsConnected(false);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnection();
    
    // Set up periodic connection checks
    const intervalId = setInterval(() => {
      if (!isConnected) {
        checkConnection();
      }
    }, 30000); // Check every 30 seconds if disconnected
    
    return () => clearInterval(intervalId);
  }, [errorType]);

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    checkConnection();
  };

  if (!isConnected && !isChecking) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }
    
    return (
      <ConnectionError 
        onRetry={handleRetry} 
        errorType={errorType} 
      />
    );
  }

  return <>{children}</>;
};

export default ConnectionErrorBoundary;