
import React, { useState, useEffect } from 'react';
import { Target, Plus, Edit, Trash2, TrendingUp, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useUser } from '@supabase/auth-helpers-react';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL! as string;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY! as string;
const supabase = createClient(supabaseUrl, supabaseKey);

const ContentPillars = () => {
  const [activeTab, setActiveTab] = useState("strategy");
  const [pillars, setPillars] = useState([]);
  const { user } = useUser();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newPillar, setNewPillar] = useState({ name: '', description: '', targetPercentage: 0 });

  useEffect(() => {
    const fetchPillarsData = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('content_pillars')
          .select('*')
          .eq('user_id', user.id);
          
        if (error) throw error;
        
        // Get video counts for each pillar
        const { data: videos, error: videosError } = await supabase
          .from('videos')
          .select('id, pillar_id, views, status')
          .eq('user_id', user.id)
          .eq('status', 'published')
          .not('youtube_video_id', 'is', null);
          
        if (videosError) throw videosError;
        
        // Add video counts to pillars
        const pillarsWithCounts = data.map(pillar => {
          const pillarVideos = videos.filter(v => v.pillar_id === pillar.id);
          const videoCount = pillarVideos.length;
          const totalVideos = videos.length;
          const actualPercentage = totalVideos > 0 ? Math.round((videoCount / totalVideos) * 100) : 0;
          
          return {
            ...pillar,
            videoCount: videoCount,
            actualPercentage: actualPercentage
          };
        });
        
        setPillars(pillarsWithCounts);
      } catch (error) {
        console.error('Error fetching pillars:', error);
      }
    };
    
    fetchPillarsData();
  }, [user]);

  const pieData = pillars.map(pillar => ({
    name: pillar.name,
    value: pillar.actualPercentage,
    color: pillar.color
  }));

  const performanceData = pillars.map(pillar => ({
    name: pillar.name,
    target: pillar.targetPercentage,
    actual: pillar.actualPercentage,
    videos: pillar.videoCount
  }));

  const addPillar = () => {
    if (newPillar.name && newPillar.description) {
      const colors = ['#37BEB0', '#E76F51', '#FF7B25', '#F59F0A', '#8B5CF6', '#06B6D4'];
      setPillars([...pillars, {
        id: Date.now(),
        ...newPillar,
        actualPercentage: 0,
        videoCount: 0,
        color: colors[pillars.length % colors.length]
      }]);
      setNewPillar({ name: '', description: '', targetPercentage: 0 });
      setIsDialogOpen(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-white">Content Pillars</h1>
          <p className="text-gray-400 mt-1">Organize your content strategy around key themes</p>
        </div>
      </div>

      {/* Tabs Navigation */}
      <Tabs defaultValue="strategy" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="glass-effect border border-gray-700">
          <TabsTrigger value="strategy" className="data-[state=active]:bg-blue-600">
            Strategy Overview
          </TabsTrigger>
          <TabsTrigger value="published" className="data-[state=active]:bg-blue-600">
            Published Content
          </TabsTrigger>
          <TabsTrigger value="assignment" className="data-[state=active]:bg-blue-600">
            Needs Assignment
          </TabsTrigger>
          <TabsTrigger value="schedule" className="data-[state=active]:bg-blue-600">
            Optimal Schedule
          </TabsTrigger>
        </TabsList>

        <TabsContent value="strategy" className="mt-6">
          {/* Strategy Overview Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <div className="glass-effect lg:col-span-2">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-teal" />
                  <CardTitle className="text-white">Strategy Recommendations Available</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">
                  Get personalized insights about your content strategy, pillar balance, and growth
                  opportunities based on your current performance data.
                </p>
                <Button className="glass-button bg-teal/20 hover:bg-teal/30 text-white border-teal/30">
                  <Eye className="w-4 h-4 mr-2" />
                  View Pillar Insights
                </Button>
              </CardContent>
            </div>
            <div className="glass-effect">
              <CardHeader>
                <CardTitle className="text-white">Content Balance</CardTitle>
              </CardHeader>
              <CardContent>
                {pillars.map(pillar => (
                  <div key={pillar.id} className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: pillar.color }}></div>
                      <span className="text-gray-300">{pillar.name}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-400">{pillar.actualPercentage}% / {pillar.targetPercentage}%</span>
                      <span className="ml-2 text-orange-400">
                        {Math.abs(pillar.actualPercentage - pillar.targetPercentage)}% Under
                      </span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </div>
          </div>

          {/* Pillar Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {pillars.map(pillar => {
              const variance = pillar.actualPercentage - pillar.targetPercentage;
              const isUnder = variance < 0;
              
              return (
                <Card key={pillar.id} className="glass-effect hover:border-white/30 transition-all">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full" style={{ backgroundColor: pillar.color }}></div>
                      <CardTitle className="text-white text-lg">{pillar.name}</CardTitle>
                    </div>
                    <p className="text-sm text-gray-400">Target: {pillar.targetPercentage}% of content</p>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-1">
                      <p className="text-sm text-gray-400">Progress</p>
                      <Progress
                        value={pillar.actualPercentage}
                        max={pillar.targetPercentage}
                        className="h-2 bg-gray-700"
                      />
                      <div className="flex justify-between text-sm">
                        <span className="text-orange-400">
                          {isUnder ? Math.abs(variance).toFixed(1) + '% Under' : variance.toFixed(1) + '% Over'}
                        </span>
                        <span className="text-gray-400">{pillar.actualPercentage}% / {pillar.targetPercentage}%</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-400">{pillar.videoCount} videos</span>
                    </div>
                    
                    <div className="flex justify-between pt-2">
                      <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white hover:bg-white/10">
                        Click to view detailed analytics
                      </Button>
                      <Button className="glass-button bg-blue-600/80 hover:bg-blue-600 text-white">
                        View Videos
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
            
            {/* Add New Pillar Card */}
            <Card className="glass-effect border-dashed border-gray-700 hover:border-white/30 transition-all">
              <CardContent className="flex flex-col items-center justify-center h-full py-8">
                <div className="w-12 h-12 rounded-full bg-gray-700/50 flex items-center justify-center mb-4">
                  <Plus className="w-6 h-6 text-gray-400" />
                </div>
                <h3 className="text-white text-lg font-medium mb-1">Add New Pillar</h3>
                <p className="text-gray-400 text-sm mb-4">{pillars.length}/5 pillars (ai_pro)</p>
                
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="glass-button bg-teal/20 hover:bg-teal/30 text-white border-teal/30">
                      <Plus className="w-4 h-4 mr-2" />
                      Create Pillar
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="glass-modal sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle className="text-white">Create New Content Pillar</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div className="space-y-2">
                        <label className="text-sm text-gray-300">Pillar Name</label>
                        <Input 
                          value={newPillar.name}
                          onChange={(e) => setNewPillar({...newPillar, name: e.target.value})}
                          placeholder="e.g., Product Reviews"
                          className="bg-gray-800 border-gray-700 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm text-gray-300">Description</label>
                        <Input 
                          value={newPillar.description}
                          onChange={(e) => setNewPillar({...newPillar, description: e.target.value})}
                          placeholder="Brief description of this content pillar"
                          className="bg-gray-800 border-gray-700 text-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm text-gray-300">Target Percentage</label>
                        <Input 
                          type="number"
                          value={newPillar.targetPercentage}
                          onChange={(e) => setNewPillar({...newPillar, targetPercentage: parseInt(e.target.value) || 0})}
                          placeholder="e.g., 25"
                          className="bg-gray-800 border-gray-700 text-white"
                        />
                        <p className="text-xs text-gray-500">What percentage of your content should be in this pillar?</p>
                      </div>
                    </div>
                    <div className="flex justify-end gap-3">
                      <Button 
                        variant="outline" 
                        onClick={() => setIsDialogOpen(false)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={addPillar}
                        className="glass-button bg-teal/20 hover:bg-teal/30 text-white border-teal/30"
                      >
                        Create Pillar
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
                
                <p className="text-xs text-gray-500 mt-4">Upgrade your plan to add more pillars</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Tab content containers */}
        <TabsContent value="published" className="mt-6">
          <div className="glass-effect p-8 text-center">
            <h3 className="text-white text-xl mb-2">Published Content</h3>
            <p className="text-gray-400">View and manage your published content across pillars</p>
          </div>
        </TabsContent>

        <TabsContent value="assignment" className="mt-6">
          <div className="glass-effect p-8 text-center">
            <h3 className="text-white text-xl mb-2">Needs Assignment</h3>
            <p className="text-gray-400">Content that needs to be assigned to pillars</p>
          </div>
        </TabsContent>

        <TabsContent value="schedule" className="mt-6">
          <div className="glass-effect p-8 text-center">
            <h3 className="text-white text-xl mb-2">Optimal Schedule</h3>
            <p className="text-gray-400">Recommended publishing schedule for balanced content</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContentPillars;
