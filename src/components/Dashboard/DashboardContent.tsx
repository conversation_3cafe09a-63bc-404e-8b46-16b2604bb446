
import { useState, Suspense, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { BarChart3, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import DashboardStats from './DashboardStats';
import GoalsSection from './GoalsSection';
import RecentActivity from './RecentActivity';
import QuickStartChecklist from './QuickStartChecklist';
import ConsolidatedInsights from './InsightsTab/ConsolidatedInsights';
import TrendingOpportunities from './TrendingOpportunities';
import TopVideosTable from '@/components/Analytics/TopVideosTable';
import { useAnalyticsData } from '@/hooks/useAnalyticsData';
import { LoadingState } from '@/components/ui/loading-state';

const PerformanceAlertsSection = () => {
  return (
    <div className="space-y-3">
      <div className="p-4 bg-purple-900/80 border border-purple-800 rounded-lg">
        <div className="flex items-start space-x-3">
          <div className="p-2 rounded bg-orange/20">
            <svg className="w-4 h-4 text-orange" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
              <path d="M12 9v4"></path>
              <path d="M12 17h.01"></path>
            </svg>
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-white text-sm mb-1">3 videos underperforming</h4>
            <p className="text-xs text-gray-300 mb-2">Recent uploads 40% below average</p>
            <button className="text-xs h-7 border border-orange/50 text-orange hover:bg-orange/10 inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 px-3">
              Analyze
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const DashboardContent = ({
  userProfile,
  pillars,
  videoCount,
  onConnectionUpdate
}) => {
  const navigate = useNavigate();
  
  // Simple state for showing QuickStart
  const [showQuickStart] = useState(
    userProfile && !userProfile?.onboarding_completed
  );

  // Add date range state for analytics data
  const [dateRange] = useState({
    start: new Date(new Date().setDate(new Date().getDate() - 30)),
    end: new Date()
  });

  // Convert DateRange to string format expected by useAnalyticsData
  const dateRangeString = useMemo(() => {
    const diffTime = Math.abs(dateRange.end.getTime() - dateRange.start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays}d`;
  }, [dateRange]);

  // Fetch analytics data for top videos table
  const { data: analyticsData, isLoading: analyticsLoading } = useAnalyticsData(dateRangeString);

  // Add safety check for userProfile
  if (!userProfile) {
    return (
      <div className="w-full px-8 pb-8">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-white">Loading user profile...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-8 pb-8">
      {/* Dashboard Grid Layout - Full width, single column */}
      <div className="grid grid-cols-1 gap-8 min-h-screen">
        {/* Dashboard Stats - Tier 1 (Primary) */}
        <LoadingState isLoading={!pillars} variant="skeleton">
          <div className="dashboard-card tier-1">
            <DashboardStats user={userProfile} />
          </div>
        </LoadingState>

        {/* Quick Start Checklist (conditionally rendered) - Tier 2 */}
        {showQuickStart && (
          <div className="dashboard-card tier-2">
            <QuickStartChecklist
              user={userProfile}
              onComplete={() => {
                // Refresh data when checklist is completed
                onConnectionUpdate();
              }}
              navigate={navigate}
            />
          </div>
        )}

        {/* Goals Section - Tier 1 (Primary) */}
        <div className="dashboard-card tier-1">
          <GoalsSection user={userProfile} navigate={navigate} />
        </div>

        {/* Recent Activity Container - Tier 2 (Secondary) */}
        <div className="dashboard-card tier-2">
          <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
          <RecentActivity user={userProfile} />
        </div>

        {/* Insights Section - Tier 3 (Tertiary) */}
        <div className="dashboard-card tier-3">
          <h2 className="text-xl font-semibold text-white mb-4">Insights</h2>
          <PerformanceAlertsSection />
        </div>

        {/* Trending Opportunities - Tier 2 (Secondary) */}
        <div className="dashboard-card tier-2">
          <h2 className="text-xl font-semibold mb-4">Trending Opportunities</h2>
          <TrendingOpportunities />
        </div>
      </div>
    </div>
  );
};

// Wrap the component with error boundary and suspense
const DashboardContentWrapper = (props: any) => {
  return (
    <Suspense fallback={
      <div className="w-full px-8 pb-8 flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading dashboard...</p>
        </div>
      </div>
    }>
      <DashboardContent {...props} />
    </Suspense>
  );
};

export default DashboardContentWrapper;
