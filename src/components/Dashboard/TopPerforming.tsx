import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Trophy, Eye, Video } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';

const TopPerforming: React.FC = () => {
  const { user } = useAuth();
  const [videos, setVideos] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchTopVideos = async () => {
      if (!user) return;
      
      try {
        // First, ensure the mock video is removed from the database
        await supabase
          .from('videos')
          .delete()
          .match({ user_id: user.id })
          .or('title.ilike.%Boost Your Work From Home%,title.eq.Boost Your Work From Home Setup');
        
        // Then fetch real videos
        const { data, error } = await supabase
          .from('videos')
          .select(`
            id,
            title,
            views,
            youtube_thumbnail_url,
            content_pillars(name)
          `)
          .eq('user_id', user.id)
          .eq('status', 'published')
          .not('youtube_video_id', 'is', null)
          .not('title', 'ilike', '%Boost%') // Exclude anything with Boost in the title
          .order('views', { ascending: false })
          .limit(3);
          
        if (error) throw error;
        
        if (data && data.length > 0) {
          // Extra aggressive client-side filtering
          const filteredData = data.filter(video => 
            !video.title.toLowerCase().includes('boost') &&
            !video.title.includes('work from home')
          );
          
          const formattedVideos = filteredData.map(video => ({
            id: video.id,
            title: video.title,
            views: video.views || 0,
            thumbnail: video.youtube_thumbnail_url,
            category: video.content_pillars?.name || null
          }));
          
          setVideos(formattedVideos);
        }
      } catch (err) {
        console.error('Error fetching top videos:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTopVideos();
  }, [user]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!videos.length) {
    return (
      <div className="bg-sidebar-background/80 border-sidebar-border overflow-hidden">
        <CardContent className="p-4">
          <div className="flex justify-between items-start mb-4">
            <h3 className="text-base font-medium">Top Performing</h3>
            <Trophy className="text-yellow-400 w-5 h-5" />
          </div>
          <div className="text-center">
            <p className="text-gray-500">No top performing videos found.</p>
          </div>
        </CardContent>
      </div>
    );
  }

  return (
    <Card className="bg-sidebar-background/80 border-sidebar-border overflow-hidden">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-base font-medium">Top Performing</h3>
          <Trophy className="text-yellow-400 w-5 h-5" />
        </div>
        
        <div className="space-y-4">
          {videos.map((video) => (
            <div key={video.id} className="flex items-center">
              <div className="w-12 h-12 rounded overflow-hidden flex-shrink-0 mr-3">
                {video.thumbnail ? (
                  <img src={video.thumbnail} alt={video.title} className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                    <Video className="w-6 h-6 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium truncate">{video.title}</h4>
                <div className="flex items-center mt-1">
                  <Eye className="w-3.5 h-3.5 mr-1 opacity-90" />
                  <span className="text-xs opacity-90">{video.views} views</span>
                </div>
              </div>
              {video.category && (
                <span className="ml-2 px-2 py-1 text-xs font-medium rounded bg-orange-500/20 text-orange-400">
                  {video.category}
                </span>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TopPerforming;
