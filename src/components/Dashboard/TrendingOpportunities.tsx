import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { TrendingUp, ExternalLink, Sparkles, ArrowRight, Flame } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { runDataHealthCheck } from '@/utils/diagnostics';
import { useNavigate } from 'react-router-dom';

interface TrendingTopic {
  topic: string;
  growth: number;
  opportunity: string;
  relevance: number;
}

interface TrendingOpportunitiesProps {
  navigate?: (path: string) => void;
}

const TrendingOpportunities: React.FC<TrendingOpportunitiesProps> = ({ navigate: propNavigate }) => {
  // Use provided navigate or get from hook
  const hookNavigate = useNavigate();
  const navigate = propNavigate || hookNavigate;
  
  const handleViewAllTrends = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigate('/analytics/trends');
  };
  
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);
  const [healthCheckResults, setHealthCheckResults] = useState<any>(null);

  useEffect(() => {
    if (user) {
      fetchTrendingTopics();
      performHealthCheck();
    }
  }, [user]);

  const performHealthCheck = async () => {
    if (!user) return;
    try {
      const results = await runDataHealthCheck(user.id);
      setHealthCheckResults(results);
    } catch (error) {
      console.error('Health check failed:', error);
    }
  };

  const fetchTrendingTopics = async () => {
    try {
      setIsLoading(true);

      // Get user's content pillars
      const { data: pillars } = await supabase
        .from('content_pillars')
        .select('name, keywords')
        .eq('user_id', user?.id);

      console.log('📊 Content pillars found:', pillars?.length || 0, pillars);

      // Get recent videos to analyze performance patterns
      const { data: videos } = await supabase
        .from('videos')
        .select('title, views, published_at, content_pillars(name)')
        .eq('user_id', user?.id)
        .eq('status', 'published')
        .order('published_at', { ascending: false })
        .limit(20);

      console.log('🎥 Published videos found:', videos?.length || 0);
      console.log('Videos with views:', videos?.filter(v => v.views > 0).length || 0);
      console.log('Videos with pillars:', videos?.filter(v => v.content_pillars?.name).length || 0);

      // Generate trending topics based on user's content and performance
      const topics: TrendingTopic[] = generateTrendingTopics(pillars, videos);
      console.log('🎯 Generated trending topics:', topics);
      setTrendingTopics(topics);

    } catch (error) {
      console.error('❌ Error fetching trending topics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateTrendingTopics = (pillars: any[], videos: any[]) => {
    // This is where you'd implement your trend analysis logic
    // For now, we'll return some example trends based on the user's content
    const topics: TrendingTopic[] = [];

    if (pillars?.length && videos?.length) {
      // Find best performing content areas
      const pillarPerformance = videos.reduce((acc: any, video) => {
        const pillarName = video.content_pillars?.name;
        if (pillarName) {
          if (!acc[pillarName]) {
            acc[pillarName] = { views: 0, count: 0 };
          }
          acc[pillarName].views += video.views || 0;
          acc[pillarName].count += 1;
        }
        return acc;
      }, {});

      // Convert performance data into trending topics
      Object.entries(pillarPerformance).forEach(([pillar, stats]: [string, any]) => {
        const avgViews = stats.views / stats.count;
        topics.push({
          topic: `${pillar} Deep Dives`,
          growth: Math.round((avgViews / 1000) * 100) / 100,
          opportunity: 'High-value content opportunity',
          relevance: 95
        });
      });

      // Add some general trending topics
      topics.push({
        topic: 'Content Creation Tips',
        growth: 25,
        opportunity: 'Growing search interest',
        relevance: 90
      });
    }

    return topics.slice(0, 3); // Return top 3 opportunities
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="w-6 h-6 border-2 border-cyan-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div>
      {trendingTopics.length > 0 ? (
        <div className="space-y-4">
          {trendingTopics.map((topic, index) => (
            <div
              key={index}
              className="p-4 bg-gray-700/20 rounded-lg border border-gray-600/30 hover:border-cyan-500/30 transition-all duration-200"
            >
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-white flex items-center">
                    <Flame className="w-4 h-4 text-orange mr-2" />
                    {topic.topic}
                  </h4>
                  <p className="text-sm text-gray-400 mt-1">{topic.opportunity}</p>
                </div>
                <div className="text-right">
                  <div className="text-cyan-500 font-medium">+{topic.growth}K views</div>
                  <div className="text-xs text-gray-400">potential</div>
                </div>
              </div>
              <div className="mt-3 flex items-center justify-between">
                <div className="text-xs text-gray-400">
                  Relevance score: {topic.relevance}%
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-cyan-500 hover:text-cyan-500 hover:bg-cyan-500/10"
                >
                  Explore
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          ))}
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewAllTrends}
            className="mt-4 border-cyan-500 text-cyan-500 hover:bg-cyan-500 hover:text-white"
          >
            View All Trends
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      ) : (
        <div className="text-center py-12">
          <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            No Trending Topics Yet
          </h3>
          <p className="text-gray-400 text-sm mb-6">
            Add more content and engage with your audience to discover trending opportunities.
          </p>
          <Button
            variant="outline"
            className="text-cyan-500 border-cyan-500 hover:bg-cyan-500 hover:text-white"
            onClick={handleViewAllTrends}
          >
            View All Trends
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default TrendingOpportunities;
