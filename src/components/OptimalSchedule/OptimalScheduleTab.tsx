
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Copy, TrendingUp, Clock } from 'lucide-react';
import ScheduleHeatmap from './ScheduleHeatmap';
import SimplifiedScheduleFlow from './SimplifiedScheduleFlow';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  actual_percentage: number;
  video_count: number;
  avg_views: number;
  best_day: string;
  best_day_boost: number;
}

interface OptimalScheduleTabProps {
  pillars: ContentPillar[];
}

const OptimalScheduleTab = ({ pillars }: OptimalScheduleTabProps) => {
  return (
    <div className="space-y-8">
      {/* Simplified Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-white">Your Optimal Publishing Schedule</h2>
        <p className="text-gray-300">Based on your historical performance data from the last 90 days</p>
      </div>

      {/* Performance Heatmap - Simplified */}
      <Card className="bg-gray-800 border-gray-600">
        <CardHeader>
          <CardTitle className="text-white flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-teal" />
            <span>When Your Content Performs Best</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScheduleHeatmap />
        </CardContent>
      </Card>

      {/* Simplified Schedule Flow - Replaces the stepped approach */}
      <SimplifiedScheduleFlow pillars={pillars} />
    </div>
  );
};

export default OptimalScheduleTab;
