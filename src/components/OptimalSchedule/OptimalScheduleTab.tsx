
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Copy, TrendingUp, Clock, ChevronLeft, ChevronRight } from 'lucide-react';
import ScheduleHeatmap from './ScheduleHeatmap';
import SteppedScheduleFlow from './SteppedScheduleFlow';

interface ContentPillar {
  id: string;
  name: string;
  color: string;
  actual_percentage: number;
  video_count: number;
  avg_views: number;
  best_day: string;
  best_day_boost: number;
}

interface OptimalScheduleTabProps {
  pillars: ContentPillar[];
}

const OptimalScheduleTab = ({ pillars }: OptimalScheduleTabProps) => {
  const [selectedCell, setSelectedCell] = useState<{ pillar: string; day: string } | null>(null);

  const handleApplySchedule = () => {
    // In a real implementation, this would navigate to calendar page with pre-filled schedule
    console.log('Applying schedule to calendar...');
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-white">Best Days to Publish</h2>
        <p className="text-gray-300">Discover when your content performs best and optimize your publishing schedule</p>
        <div className="inline-flex items-center space-x-2 text-sm text-gray-400">
          <Calendar className="w-4 h-4" />
          <span>Analyzing last 90 days</span>
        </div>
      </div>

      {/* Performance Heatmap - Primary Visual Element */}
      <ScheduleHeatmap />

      {/* Stepped Schedule Flow - Replaces the 4 separate widgets */}
      <SteppedScheduleFlow 
        pillars={pillars} 
        onApplySchedule={handleApplySchedule}
      />
    </div>
  );
};

export default OptimalScheduleTab;
