// YouTube OAuth Configuration
// This file ensures consistency between OAuth flow and token refresh

export const YOUTUBE_CONFIG = {
  // Google OAuth Client Configuration
  CLIENT_ID: '343487815633-rtm8h3ptunici9q3f384e2q66clfohu3.apps.googleusercontent.com',
  
  // The client secret is now stored securely on the backend and is not needed here.
  
  // OAuth Scopes
  SCOPES: [
    'https://www.googleapis.com/auth/youtube.readonly',
    'https://www.googleapis.com/auth/yt-analytics.readonly',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile'
  ],
  
  // API Endpoints
  ENDPOINTS: {
    TOKEN_INFO: 'https://www.googleapis.com/oauth2/v1/tokeninfo',
    TOKEN_REFRESH: 'https://oauth2.googleapis.com/token',
    OAUTH_AUTHORIZE: 'https://accounts.google.com/o/oauth2/v2/auth'
  }
} as const;

// Validation function to ensure configuration is correct
export const validateYouTubeConfig = () => {
  const { CLIENT_ID } = YOUTUBE_CONFIG;

  if (!CLIENT_ID || !CLIENT_ID.includes('.apps.googleusercontent.com')) {
    throw new Error('Invalid YouTube Client ID configuration');
  }

  console.log('✅ YouTube configuration validated:', {
    clientId: CLIENT_ID,
    scopesCount: YOUTUBE_CONFIG.SCOPES.length
  });

  return true;
};

// Helper function to get OAuth URL
export const buildOAuthUrl = (redirectUri: string, state?: string) => {
  validateYouTubeConfig();
  
  const params = new URLSearchParams({
    client_id: YOUTUBE_CONFIG.CLIENT_ID,
    redirect_uri: redirectUri,
    scope: YOUTUBE_CONFIG.SCOPES.join(' '),
    response_type: 'code',
    access_type: 'offline',
    prompt: 'consent',
    include_granted_scopes: 'true'
  });
  
  if (state) {
    params.append('state', state);
  }
  
  return `${YOUTUBE_CONFIG.ENDPOINTS.OAUTH_AUTHORIZE}?${params.toString()}`;
};
