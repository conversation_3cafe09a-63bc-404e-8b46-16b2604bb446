import { useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  const checkAndRefreshSubscription = async (userId: string) => {
    try {
      console.log('🔄 Refreshing subscription data for user:', userId);

      const { data, error } = await supabase.functions.invoke('check-subscription');
      if (error) {
        console.error('❌ Error checking subscription:', error);
      } else {
        console.log('✅ Subscription check result:', data);
      }
    } catch (error) {
      console.error('❌ Error in subscription refresh:', error);
    }
  };

  const createUserProfile = async (user: User) => {
    try {
      // Validate user data before creating profile
      if (!user.id || !user.email) {
        console.error('Invalid user data for profile creation');
        return;
      }

      // Sanitize user inputs
      const sanitizedEmail = user.email.toLowerCase().trim();
      const sanitizedName = user.user_metadata?.name?.substring(0, 100) || null;

      // Check if profile already exists first
      const { data: existingProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('id', user.id)
        .single();

      if (!existingProfile) {
        console.log('Creating new user profile for:', user.id);
        const { error: profileError } = await supabase
          .from('profiles')
          .insert([
            {
              id: user.id,
              email: sanitizedEmail,
              subscription_tier: 'starter',
              subscription_status: 'trialing'
            }
          ]);
        
        if (profileError && !profileError.message.includes('duplicate')) {
          console.error('Error creating user profile:', profileError);
        }
      }

      // Also ensure users table entry exists
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('id', user.id)
        .single();

      if (!existingUser) {
        console.log('Creating users table entry for:', user.id);
        const { error: userError } = await supabase
          .from('users')
          .insert([
            {
              id: user.id,
              email: sanitizedEmail,
              name: sanitizedName,
            }
          ]);
        
        if (userError && !userError.message.includes('duplicate')) {
          console.error('Error creating user entry:', userError);
        }
      }
    } catch (error) {
      console.error('Error in createUserProfile:', error);
    }
  };

  function validateSession(session: Session | null): boolean {
    if (!session) {
      console.log('Session is null');
      return false;
    }
    
    // Check if token is expired
    const expiresAt = session.expires_at ? new Date(session.expires_at * 1000) : null;
    const isExpired = expiresAt ? expiresAt < new Date() : true;
    
    if (isExpired) {
      console.warn('Session token is expired:', {
        expiresAt: expiresAt?.toISOString(),
        now: new Date().toISOString()
      });
      return false;
    }
    
    // Check if user exists in session
    if (!session.user) {
      console.warn('Session has no user data');
      return false;
    }
    
    console.log('Session is valid');
    return true;
  }

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, !!session);
        
        // Validate session before using it
        const isValidSession = validateSession(session);
        
        setSession(isValidSession ? session : null);
        setUser(isValidSession ? session?.user ?? null : null);
        setLoading(false);

        if (event === 'SIGNED_IN' && isValidSession && session?.user) {
          // Always refresh subscription data on sign in (handles both sign-in and sign-up)
          setTimeout(async () => {
            await createUserProfile(session.user);
            await checkAndRefreshSubscription(session.user.id);
          }, 100);
        }

        if (event === 'SIGNED_OUT') {
          // Clear any cached data on sign out
          setUser(null);
          setSession(null);
        }
      }
    );

    // THEN check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      const isValidSession = validateSession(session);
      
      setSession(isValidSession ? session : null);
      setUser(isValidSession ? session?.user ?? null : null);
      setLoading(false);

      // If user is already signed in with valid session, refresh their subscription data
      if (isValidSession && session?.user) {
        setTimeout(() => {
          checkAndRefreshSubscription(session.user.id);
        }, 500);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  return { user, session, loading };
};
