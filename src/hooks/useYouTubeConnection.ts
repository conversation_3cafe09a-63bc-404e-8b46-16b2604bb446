import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { useQuery } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { YOUTUBE_CONFIG, validateYouTubeConfig } from '@/config/youtube';

interface YouTubeConnectionData {
  id: string;
  youtube_channel_id?: string;
  youtube_channel_name?: string;
  youtube_access_token?: string;
  youtube_refresh_token?: string;
  youtube_subscriber_baseline?: number;
  youtube_thumbnail_url?: string;
  last_youtube_sync?: string;
  preferences?: string;
}

interface TokenValidationResult {
  isValid: boolean;
  needsRefresh: boolean;
  error?: string;
}

export const useYouTubeConnection = () => {
  const { user } = useAuth();

  // State for token validation - we'll manage this internally to avoid circular dependency
  const [tokenStatus, setTokenStatus] = useState<TokenValidationResult>({
    isValid: false,
    needsRefresh: false
  });
  const [isValidatingToken, setIsValidatingToken] = useState(false);

  // Fetch YouTube connection data using React Query for consistent caching
  const {
    data: youtubeData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['youtube-connection', user?.id],
    queryFn: async () => {
      if (!user?.id) {
        console.log('🚫 No user ID available for YouTube connection');
        return null;
      }

      console.log('🔄 Fetching YouTube connection data for user:', user.id);

      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          youtube_channel_id,
          youtube_channel_name,
          youtube_access_token,
          youtube_refresh_token,
          youtube_subscriber_baseline,
          youtube_thumbnail_url,
          last_youtube_sync,
          preferences
        `)
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('❌ Error fetching YouTube data:', error);
        throw error;
      }

      console.log('✅ YouTube data fetched:', {
        channelId: data?.youtube_channel_id,
        channelName: data?.youtube_channel_name,
        hasAccessToken: !!data?.youtube_access_token,
        hasRefreshToken: !!data?.youtube_refresh_token,
        subscriberCount: data?.youtube_subscriber_baseline,
        lastSync: data?.last_youtube_sync,
        preferences: data?.preferences,
        accessTokenLength: data?.youtube_access_token?.length || 0,
        refreshTokenLength: data?.youtube_refresh_token?.length || 0
      });

      return data as YouTubeConnectionData;
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  });

  // Token validation functions
  const validateTokenWithYouTubeAPI = async (accessToken: string): Promise<TokenValidationResult> => {
    try {
      console.log('🔍 Validating token with YouTube API...');

      // Try to make a simple YouTube API call to verify the token works
      const response = await fetch(
        'https://www.googleapis.com/youtube/v3/channels?part=snippet&mine=true',
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Accept': 'application/json'
          }
        }
      );

      console.log('YouTube API validation response:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ YouTube API validation successful, channels found:', data.items?.length || 0);
        return { isValid: true, needsRefresh: false };
      } else if (response.status === 401) {
        console.log('❌ YouTube API validation failed - unauthorized');
        return { isValid: false, needsRefresh: true, error: 'Token expired' };
      } else {
        console.log('❌ YouTube API validation failed - other error:', response.status);
        return { isValid: false, needsRefresh: false, error: `API error: ${response.status}` };
      }
    } catch (error) {
      console.error('YouTube API validation error:', error);
      return { isValid: false, needsRefresh: false, error: 'API validation failed' };
    }
  };

  const validateToken = async (accessToken: string, retryCount = 0): Promise<TokenValidationResult> => {
    try {
      console.log('=== VALIDATING YOUTUBE TOKEN ===');
      console.log('Token length:', accessToken?.length || 0);
      console.log('Retry count:', retryCount);

      // First try YouTube API validation (more reliable for new tokens)
      const youtubeResult = await validateTokenWithYouTubeAPI(accessToken);
      if (youtubeResult.isValid) {
        return youtubeResult;
      }

      // If YouTube API fails, try Google's tokeninfo as fallback
      console.log('🔄 YouTube API validation failed, trying tokeninfo...');
      const response = await fetch(
        `${YOUTUBE_CONFIG.ENDPOINTS.TOKEN_INFO}?access_token=${accessToken}`
      );

      const data = await response.json();
      console.log('Token validation response:', response.status, data);

      if (response.ok && data.scope?.includes('youtube')) {
        console.log('✅ Token validation successful via tokeninfo');
        return { isValid: true, needsRefresh: false };
      } else if (response.status === 400 && data.error === 'invalid_token') {
        // For newly issued tokens, Google's tokeninfo might take a moment to recognize them
        // Retry once after a short delay
        if (retryCount === 0) {
          console.log('⏱️ Token validation failed, retrying in 2 seconds...');
          await new Promise(resolve => setTimeout(resolve, 2000));
          return validateToken(accessToken, retryCount + 1);
        }
        console.log('❌ Token validation failed after retry');
        return { isValid: false, needsRefresh: true, error: 'Token expired' };
      } else {
        console.log('❌ Token validation failed - invalid scope');
        return { isValid: false, needsRefresh: false, error: 'Invalid token scope' };
      }
    } catch (error) {
      console.error('Token validation error:', error);
      return { isValid: false, needsRefresh: false, error: 'Validation failed' };
    }
  };

  const refreshAccessToken = async (refreshToken: string): Promise<string | null> => {
    try {
      console.log('=== REFRESHING YOUTUBE TOKEN (SECURE) ===');
      
      const { data, error } = await supabase.functions.invoke('youtube-token-refresh', {
        body: { refreshToken },
      });

      if (error) {
        console.error('❌ Token refresh function error:', error);
        throw error;
      }

      if (!data.success) {
        console.error('❌ Token refresh failed:', data.error);
        throw new Error(data.error);
      }
      
      console.log('✅ Access token refreshed successfully via Edge Function');

      // Invalidate the query to refetch fresh data with the new token
      refetch();

      return data.accessToken;

    } catch (error) {
      console.error('Token refresh error:', error);
      // Set a specific status to indicate reconnection is needed
      setTokenStatus({
        isValid: false,
        needsRefresh: false,
        error: 'Refresh failed. Please reconnect.',
      });
      return null;
    }
  };

  const checkTokenStatus = async () => {
    console.log('🔍 Starting token validation check...');
    console.log('📋 Current YouTube data for validation:', {
      hasAccessToken: !!youtubeData?.youtube_access_token,
      hasChannelId: !!youtubeData?.youtube_channel_id,
      accessTokenLength: youtubeData?.youtube_access_token?.length || 0,
      channelId: youtubeData?.youtube_channel_id,
      lastSync: youtubeData?.last_youtube_sync
    });

    if (!youtubeData?.youtube_access_token || !youtubeData?.youtube_channel_id) {
      console.log('❌ No YouTube connection data found for validation');
      setTokenStatus({
        isValid: false,
        needsRefresh: false,
        error: 'No YouTube connection found'
      });
      return;
    }

    console.log('🔄 Starting token validation...');
    setIsValidatingToken(true);

    try {
      let result = await validateToken(youtubeData.youtube_access_token);

      if (!result.isValid && result.needsRefresh && youtubeData.youtube_refresh_token) {
        console.log('Attempting to refresh expired token...');
        const newToken = await refreshAccessToken(youtubeData.youtube_refresh_token);

        if (newToken) {
          result = await validateToken(newToken);
          if (result.isValid) {
            toast({
              title: 'Success',
              description: 'YouTube token refreshed successfully',
            });
          }
        } else {
          result.error = 'Failed to refresh token - please reconnect';
        }
      }

      console.log('📊 Final token validation result:', {
        isValid: result.isValid,
        needsRefresh: result.needsRefresh,
        error: result.error
      });
      setTokenStatus(result);
    } catch (error) {
      console.error('❌ Token status check failed:', error);
      setTokenStatus({
        isValid: false,
        needsRefresh: false,
        error: 'Failed to check token status'
      });
    } finally {
      setIsValidatingToken(false);
      console.log('✅ Token validation completed');
    }
  };

  // Auto-validate token when YouTube data changes
  useEffect(() => {
    console.log('🔄 useEffect triggered for token validation:', {
      hasAccessToken: !!youtubeData?.youtube_access_token,
      hasUser: !!user,
      accessTokenLength: youtubeData?.youtube_access_token?.length || 0,
      channelId: youtubeData?.youtube_channel_id
    });

    if (youtubeData?.youtube_access_token && user) {
      console.log('✅ Conditions met, triggering token validation...');
      checkTokenStatus();
    } else {
      console.log('❌ Conditions not met for token validation');
    }
  }, [youtubeData?.youtube_access_token, youtubeData?.youtube_channel_id, user]);

  // More accurate connection logic: Must have valid tokens to be truly connected
  const hasChannel = !!youtubeData?.youtube_channel_id;
  const hasRecentSync = youtubeData?.last_youtube_sync &&
    new Date(youtubeData.last_youtube_sync) > new Date(Date.now() - 24 * 60 * 60 * 1000); // Within 24 hours

  // Consider connected only if we have a channel AND valid tokens
  // Recent sync alone is not enough if tokens are expired
  const isConnected = hasChannel && tokenStatus.isValid;

  // Determine if we have a channel but expired tokens (needs reconnection)
  const needsReconnection = hasChannel && !tokenStatus.isValid && tokenStatus.error;

  console.log('=== CONNECTION STATUS DEBUG ===');
  console.log('Has Channel:', hasChannel);
  console.log('Token Valid:', tokenStatus.isValid);
  console.log('Has Recent Sync:', hasRecentSync);
  console.log('Token Error:', tokenStatus.error);
  console.log('Needs Reconnection:', needsReconnection);
  console.log('Final Connected Status:', isConnected);

  return {
    // Data
    youtubeData,

    // Status
    isLoading,
    error,
    isConnected,
    hasChannel,
    needsReconnection,
    tokenStatus,
    isValidatingToken,

    // Actions
    refetch,
    checkTokenStatus,
    refreshConnection: async () => {
      console.log('🔄 Refreshing YouTube connection data...');
      await refetch();
      // Token validation will be triggered automatically by the useEffect
    }
  };
};
