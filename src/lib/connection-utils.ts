import { supabase } from './supabase-client';

export async function checkSupabaseConnection() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });
      
    if (error) {
      console.error('Supabase connection error:', error);
      return { 
        ok: false, 
        error: error.message,
        code: error.code,
        details: error.details
      };
    }
    
    return { ok: true, data };
  } catch (error) {
    console.error('Unexpected connection error:', error);
    const message = error instanceof Error ? error.message : 'Unknown connection error';
    return { ok: false, error: message };
  }
}

export async function testSupabaseConnection() {
  try {
    const startTime = performance.now();
    const result = await checkSupabaseConnection();
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);
    
    return {
      ...result,
      responseTime,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Connection test failed:', error);
    return false;
  }
}

export async function checkApiConnection(endpoint = '/api/health') {
  try {
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (!response.ok) {
      return { 
        ok: false, 
        status: response.status,
        statusText: response.statusText
      };
    }
    
    const data = await response.json();
    return { ok: true, data };
  } catch (error) {
    console.error('API connection error:', error);
    const message = error instanceof Error ? error.message : 'Unknown API error';
    return { ok: false, error: message };
  }
}