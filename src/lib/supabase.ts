import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/integrations/supabase/types';

console.log('Supabase environment check:', {
  urlExists: !!import.meta.env.VITE_SUPABASE_URL,
  keyExists: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
  urlPrefix: import.meta.env.VITE_SUPABASE_URL?.substring(0, 10) + '...',
  env: import.meta.env.MODE
});

console.log('Initializing Supabase client with URL:', 
  import.meta.env.VITE_SUPABASE_URL || 'URL not found');

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
}

// Create a type-safe client
export const supabase = createClient<Database>(
  supabaseUrl, 
  supabaseAnonKey,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      debug: import.meta.env.DEV // Enable debug mode in development
    }
  }
);

// Export typed helpers
export type Tables<T extends keyof Database['public']['Tables']> = 
  Database['public']['Tables'][T]['Row'];
export type Enums<T extends keyof Database['public']['Enums']> = 
  Database['public']['Enums'][T];

// Add this function to check connection
export const checkSupabaseConnection = async () => {
  try {
    // Simple health check query
    const { data, error } = await supabase.from('content_pillars').select('id', { count: 'exact', head: true });
    if (error) throw error;
    console.log('Supabase connection successful');
    return { ok: true };
  } catch (error) {
    console.error('Supabase connection failed:', error);
    return { ok: false, error };
  }
};

// Add this function to test connection before login
export const testSupabaseConnection = async () => {
  try {
    console.log('Testing Supabase connection...');
    console.log('URL:', supabaseUrl);
    console.log('Key exists:', !!supabaseAnonKey);

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Missing Supabase configuration');
    }

    // First test basic connectivity to the REST API
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'GET',
      headers: {
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Basic connectivity test:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    // Test a simple query that should work without authentication
    // Try to query a public table or use a simpler approach
    try {
      const { error } = await supabase.from('users').select('count', { count: 'exact', head: true });
      if (error) {
        console.warn('Users table query failed (might be due to RLS):', error.message);
        // This is expected if RLS is enabled, so we'll consider the connection successful
        // if we got this far
      }
    } catch (queryError) {
      console.warn('Database query failed, but connection seems OK:', queryError);
    }

    console.log('Supabase connection test successful');
    return true;
  } catch (error) {
    console.error('Supabase connection test failed:', error);
    return false;
  }
};

export default supabase;
