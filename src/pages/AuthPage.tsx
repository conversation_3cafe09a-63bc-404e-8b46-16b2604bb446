
import React, { useState } from 'react';
import AuthForm from '@/components/Auth/AuthForm';
import PageBackground from '@/components/Layout/PageBackground';

const AuthPage = () => {
  const [mode, setMode] = useState<'signin' | 'signup'>('signin');

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-black">
      {/* Content container */}
      <div className="flex flex-col items-center max-w-md w-full">
        {/* Logo container */}
        <div className="flex flex-col items-center mb-8">
          <h1 className="text-white text-4xl font-bold">My Content Hub</h1>
        </div>
        
        {/* Form */}
        <div className="w-full max-w-md">
          <AuthForm 
            mode={mode} 
            onToggleMode={() => setMode(mode === 'signin' ? 'signup' : 'signin')} 
          />
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
