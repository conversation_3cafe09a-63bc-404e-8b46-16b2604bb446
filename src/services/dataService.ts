import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

// Define a type for the video data
export type Video = {
  id?: string;
  user_id: string;
  title: string;
  description?: string;
  status?: string;
  pillar_id?: string;
  priority?: string;
  scheduled_date?: string;
  created_at?: string;
  youtube_video_id?: string;
  published_at?: string;
  views?: number;
  like_count?: number;
  comment_count?: number;
  youtube_thumbnail_url?: string;
};

// Centralized data service for database operations
export const dataService = {
  // Videos
  videos: {
    getAll: async (userId: string) => {
      try {
        console.log('🔍 dataService: Fetching all videos for user', userId);
        const { data, error } = await supabase
          .from('videos')
          .select(`
            id, user_id, title, description, status, 
            pillar_id, priority, scheduled_date, created_at,
            youtube_video_id, published_at, views, like_count, 
            comment_count, youtube_thumbnail_url
          `)
          .eq('user_id', userId)
          .order('created_at', { ascending: false });
          
        if (error) throw error;
        console.log('✅ dataService: Fetched videos successfully', data?.length);
        return data;
      } catch (error) {
        console.error('❌ dataService: Error fetching videos:', error);
        throw error;
      }
    },
    
    getPublished: async (userId: string) => {
      try {
        console.log('🔍 dataService: Fetching published videos for user', userId);
        const { data, error } = await supabase
          .from('videos')
          .select(`
            id, title, views, published_at, youtube_video_id,
            like_count, comment_count, youtube_thumbnail_url, pillar_id
          `)
          .eq('user_id', userId)
          .eq('status', 'published')
          .not('youtube_video_id', 'is', null)
          .order('published_at', { ascending: false });
          
        if (error) throw error;
        
        // TODO: Add an is_mock boolean flag to the videos table to filter out mock data
        const realVideos = (data || []).filter(video =>
          video.published_at &&
          video.youtube_video_id &&
          video.youtube_thumbnail_url &&
          video.title
        );
        
        console.log('✅ dataService: Fetched published videos successfully', realVideos.length);
        return realVideos;
      } catch (error) {
        console.error('❌ dataService: Error fetching published videos:', error);
        throw error;
      }
    },
    
    getById: async (videoId: string) => {
      try {
        console.log('🔍 dataService: Fetching video by ID', videoId);
        const { data, error } = await supabase
          .from('videos')
          .select('*')
          .eq('id', videoId)
          .single();
        
        if (error) throw error;
        console.log('✅ dataService: Fetched video successfully');
        return data;
      } catch (error) {
        console.error('❌ dataService: Error fetching video:', error);
        throw error;
      }
    },
    
    create: async (videoData: Video) => {
      try {
        console.log('🔍 dataService: Creating new video');
        const { data, error } = await supabase
          .from('videos')
          .insert(videoData)
          .select()
          .single();
        
        if (error) throw error;
        console.log('✅ dataService: Video created successfully');
        return data;
      } catch (error) {
        console.error('❌ dataService: Error creating video:', error);
        throw error;
      }
    },
    
    update: async (videoId: string, videoData: Partial<Video>) => {
      try {
        console.log('🔍 dataService: Updating video', videoId);
        const { data, error } = await supabase
          .from('videos')
          .update(videoData)
          .eq('id', videoId)
          .select()
          .single();
        
        if (error) throw error;
        console.log('✅ dataService: Video updated successfully');
        return data;
      } catch (error) {
        console.error('❌ dataService: Error updating video:', error);
        throw error;
      }
    },
    
    delete: async (videoId: string) => {
      try {
        console.log('🔍 dataService: Deleting video', videoId);
        const { error } = await supabase
          .from('videos')
          .delete()
          .eq('id', videoId);
        
        if (error) throw error;
        console.log('✅ dataService: Video deleted successfully');
        return true;
      } catch (error) {
        console.error('❌ dataService: Error deleting video:', error);
        throw error;
      }
    },
    
    getByPillar: async (userId: string, pillarId: string) => {
      try {
        console.log('🔍 dataService: Fetching videos for pillar', pillarId);
        const { data, error } = await supabase
          .from('videos')
          .select('*')
          .eq('user_id', userId)
          .eq('pillar_id', pillarId)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        console.log('✅ dataService: Fetched pillar videos successfully', data?.length);
        return data;
      } catch (error) {
        console.error('❌ dataService: Error fetching pillar videos:', error);
        throw error;
      }
    }
  },
  
  // Content Pillars
  pillars: {
    getAll: async (userId: string) => {
      try {
        console.log('🔍 dataService: Fetching pillars for user', userId);
        const { data, error } = await supabase
          .from('content_pillars')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: true });
          
        if (error) throw error;
        console.log('✅ dataService: Fetched pillars successfully', data?.length);
        return data;
      } catch (error) {
        console.error('❌ dataService: Error fetching pillars:', error);
        throw error;
      }
    },
    getPillarsWithStats: async (userId: string) => {
      try {
        console.log('🔍 dataService: Fetching pillars with stats for user', userId);
        
        // First get the pillars
        const { data: pillars, error: pillarsError } = await supabase
          .from('content_pillars')
          .select('*')
          .eq('user_id', userId);
          
        if (pillarsError) throw pillarsError;
        
        // Then get the videos to calculate counts
        const { data: videos, error: videosError } = await supabase
          .from('videos')
          .select('id, pillar_id, views, status, title, youtube_video_id, youtube_thumbnail_url')
          .eq('user_id', userId)
          .eq('status', 'published')
          .not('youtube_video_id', 'is', null);
          
        if (videosError) throw videosError;
        
        // Filter out mock videos
        const realVideos = videos.filter(video => 
          video.youtube_video_id && 
          video.youtube_thumbnail_url
        );
        
        // Calculate total videos for percentage calculation
        const totalVideos = realVideos.length;
        console.log('Total real videos:', totalVideos);
        
        // Calculate video counts and stats for each pillar
        const pillarsWithStats = pillars.map(pillar => {
          const pillarVideos = realVideos.filter(v => v.pillar_id === pillar.id);
          const videoCount = pillarVideos.length;
          const totalViews = pillarVideos.reduce((sum, v) => sum + (v.views || 0), 0);
          const avgViews = videoCount > 0 ? totalViews / videoCount : 0;
          
          // Calculate actual percentage
          const actualPercentage = totalVideos > 0 
            ? Math.round((videoCount / totalVideos) * 100) 
            : 0;
          
          return {
            ...pillar,
            video_count: videoCount,
            total_views: totalViews,
            avg_views: avgViews,
            actual_percentage: actualPercentage,
            target_percentage: pillar.target_percentage || 0
          };
        });
        
        console.log('✅ dataService: Fetched pillars with stats successfully', pillarsWithStats);
        return pillarsWithStats;
      } catch (error) {
        console.error('❌ dataService: Error fetching pillars with stats:', error);
        throw error;
      }
    },
  },
  
  // User Profiles
  profiles: {
    getProfile: async (userId: string) => {
      try {
        console.log('🔍 dataService: Fetching profile for user', userId);
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();
          
        if (error) throw error;
        console.log('✅ dataService: Fetched profile successfully');
        return data;
      } catch (error) {
        console.error('❌ dataService: Error fetching profile:', error);
        throw error;
      }
    },
  },
  
  // AI Usage Logging
  aiUsage: {
    logUsage: async (usageData: {
      user_id: string;
      feature_type: string;
      credits_used: number;
      metadata?: any;
    }) => {
      try {
        console.log('🔍 dataService: Logging AI usage', usageData.feature_type);
        const { data, error } = await supabase
          .from('ai_usage_logs')
          .insert(usageData);
        
        if (error) throw error;
        console.log('✅ dataService: AI usage logged successfully');
        return true;
      } catch (error) {
        console.error('❌ dataService: Error logging AI usage:', error);
        throw error;
      }
    },
    
    getUserUsage: async (userId: string) => {
      try {
        console.log('🔍 dataService: Fetching AI usage for user', userId);
        const { data, error } = await supabase
          .from('ai_usage_logs')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        console.log('✅ dataService: Fetched AI usage successfully', data?.length);
        return data;
      } catch (error) {
        console.error('❌ dataService: Error fetching AI usage:', error);
        throw error;
      }
    }
  },
};
