/* Tailwind CSS directives */
@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 10%;
    --foreground: 0 0% 98%;

    --card: 0 0% 13%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 13%;
    --popover-foreground: 0 0% 98%;

    --primary: 265 89% 66%;
    --primary-foreground: 0 0% 98%;

    --secondary: 333 71% 51%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 70%;

    --accent: 265 89% 66%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 265 89% 66%;

    --radius: 0.5rem;

    --sidebar-background: 265 89% 10%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 265 89% 66%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 333 71% 51%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 265 89% 20%;
    --sidebar-ring: 265 89% 66%;

    /* MyContentHub custom colors - Updated theme */
    --teal: #7c3aed; /* Changed to purple */
    --cyan: #ec4899; /* Changed to pink */
    --terracotta: #f97316; /* Updated orange */
    --orange: #f59e0b; /* Updated amber */
    --yellow: #fbbf24; /* Updated yellow */
    --dark-text: #ffffff;
    --light-teal: #f3e8ff; /* Light purple */
    --light-cyan: #fce7f3; /* Light pink */
    --light-terracotta: #fff7ed;
    --light-orange: #fffbeb;
    --bg-gray: #18181b;
    --dark-gray: #27272a;
    --white: #ffffff;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 70%;

    --accent: 0 0% 10%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;
    --sidebar-background: 271 81% 24%; /* Purple that matches our gradient */
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 0 0% 100%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 271 91% 65%; /* Lighter purple for accents */
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 271 81% 30%; /* Slightly lighter border */
    --sidebar-ring: 271 91% 65%;
  }
}

@layer base {
  * {
    @apply antialiased;
  }

  html {
    background: #000000;
    min-height: 100vh;
    font-family: "Montserrat", Arial, sans-serif;
    font-size: 18px;
  }

  body {
    @apply text-white font-montserrat;
    background: #0f172a !important;
    background-image: radial-gradient(
        circle at 15% 15%,
        rgba(48, 145, 249, 0.6),
        transparent 40%
      ),
      radial-gradient(
        circle at 85% 15%,
        rgba(229, 57, 181, 0.5),
        transparent 40%
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(17, 193, 76, 0.4),
        transparent 40%
      ),
      radial-gradient(
        circle at 25% 75%,
        rgba(249, 115, 22, 0.4),
        transparent 40%
      ) !important;
    color: #ffffff;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    font-size: 1.125rem; /* 18px - Base font size */
    line-height: 1.75rem; /* 28px */
  }

  #root {
    background: transparent !important;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Add the animation keyframes */
  @keyframes gradientAnimation {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Override any white backgrounds */
  .bg-white {
    background: transparent !important;
  }

  [data-sidebar="inset"] {
    background: transparent !important;
  }
}

/* Add these styles to ensure sidebar extends fully */
.sidebar-container,
[data-sidebar="sidebar"] {
  min-height: 100vh;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ensure the app container takes full height */
#root,
.app-container {
  min-height: 100vh;
  height: 100%;
}

/* Add a specific override for sidebar text */
[data-sidebar],
[data-sidebar="sidebar"],
.sidebar-container {
  color: #ffffff !important;
}

[data-sidebar] *,
[data-sidebar="sidebar"] *,
.sidebar-container * {
  color: #ffffff !important;
}

/* Override any status text that might have different colors */
.status-text,
[data-sidebar] .status-text,
[data-sidebar="sidebar"] .status-text {
  color: #ffffff !important;
}
