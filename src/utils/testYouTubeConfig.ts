// Test utility to verify YouTube configuration consistency
import { YOUTUBE_CONFIG, validateYouTubeConfig } from '@/config/youtube';

export const testYouTubeConfiguration = () => {
  console.log('🧪 Testing YouTube Configuration...');
  
  try {
    // Test 1: Basic configuration validation
    console.log('Test 1: Basic configuration validation');
    validateYouTubeConfig();
    console.log('✅ Basic validation passed');
    
    // Test 2: Display current configuration
    console.log('Test 2: Current configuration');
    console.log('Client ID:', YOUTUBE_CONFIG.CLIENT_ID);
    console.log('Scopes:', YOUTUBE_CONFIG.SCOPES);
    console.log('Endpoints:', YOUTUBE_CONFIG.ENDPOINTS);
    
    // Test 3: Verify token refresh endpoint configuration
    console.log('Test 3: Token refresh endpoint configuration');
    console.log('Token refresh URL:', YOUTUBE_CONFIG.ENDPOINTS.TOKEN_REFRESH);
    console.log('Token info URL:', YOUTUBE_CONFIG.ENDPOINTS.TOKEN_INFO);

    console.log('🎉 All YouTube configuration tests passed!');
    console.log('✅ All components should now use the same configuration');
    return true;

  } catch (error) {
    console.error('❌ YouTube configuration test failed:', error);
    return false;
  }
};
