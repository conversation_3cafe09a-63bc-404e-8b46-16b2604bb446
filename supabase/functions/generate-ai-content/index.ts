
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!openAIApiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { prompt, type, user_id } = await req.json();

    console.log(`[GENERATE-AI-CONTENT] Processing request: {
  type: "${type}",
  userId: "${user_id}"
}`);

    let systemPrompt = 'You are a helpful assistant for a YouTube content creation platform.';
    let temperature = 0.7;
    let maxTokens = 500; // Default max tokens
    let responseFormat = { type: "text" };

    // Task-specific configurations
    const typeConfigs = {
      'topic-generation': {
        systemPrompt: 'You are an expert YouTube content strategist. Generate 5 creative, engaging video topic ideas. Return a valid JSON array of objects, each with "title", "description", and "reason" keys.',
        temperature: 0.75,
        maxTokens: 600,
        responseFormat: { type: "json_object" }
      },
      'title-generation': {
        systemPrompt: 'You are an expert YouTube title writer. Create 5 compelling, click-worthy titles under 60 characters. Return a JSON array of strings.',
        temperature: 0.7,
        maxTokens: 150,
        responseFormat: { type: "json_object" }
      },
      'hook-generation': {
        systemPrompt: 'You are a script writer specializing in engaging video hooks. Generate 3 hooks (1-2 sentences each) to capture attention in the first 15 seconds. Return a JSON array of strings.',
        temperature: 0.75,
        maxTokens: 200,
        responseFormat: { type: "json_object" }
      },
      'seo-generation': {
        systemPrompt: 'You are a YouTube SEO expert. Generate optimal SEO tags and hashtags. Return a JSON object with "tags" (array of 15-20 strings) and "hashtags" (a single string of 10-15 space-separated hashtags).',
        temperature: 0.4,
        maxTokens: 300,
        responseFormat: { type: "json_object" }
      },
      'description-generation': {
        systemPrompt: 'You are an expert YouTube description writer. Create an engaging, SEO-optimized description (3-4 paragraphs). Include a clear call-to-action and relevant links placeholders.',
        temperature: 0.6,
        maxTokens: 400,
        responseFormat: { type: "text" }
      }
    };

    if (type in typeConfigs) {
      const config = typeConfigs[type];
      systemPrompt = config.systemPrompt;
      temperature = config.temperature;
      maxTokens = config.maxTokens;
      responseFormat = config.responseFormat;
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        temperature,
        max_tokens: maxTokens,
        response_format: responseFormat,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    let content = data.choices[0].message.content;

    if (responseFormat.type === 'json_object') {
      try {
        const parsedContent = JSON.parse(content);
        console.log(`[GENERATE-AI-CONTENT] Successfully parsed JSON for type: ${type}`);
        return new Response(JSON.stringify({ content: parsedContent }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      } catch (parseError) {
        console.error(`[GENERATE-AI-CONTENT] Failed to parse JSON for type: ${type}. Error: ${parseError.message}`);
        // Return an error response as we expect a valid JSON
        return new Response(JSON.stringify({ error: 'Failed to process AI response.' }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
    }

    console.log('[GENERATE-AI-CONTENT] Generated content successfully');

    return new Response(JSON.stringify({ content }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-ai-content function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
