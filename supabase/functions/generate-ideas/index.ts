
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
    if (!openAIApiKey) {
      console.error('[GENERATE-IDEAS] OpenAI API key not configured');
      throw new Error('OpenAI API key not configured. Please add your OpenAI API key in the Supabase Edge Function Secrets.');
    }

    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: user } = await supabase.auth.getUser(token);

    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { prompt, type, count = 3 } = await req.json();

    if (!prompt) {
      throw new Error('Prompt is required');
    }

    console.log(`[GENERATE-IDEAS] Generating ${type} for user ${user.user.id}`);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: getSystemPrompt(type)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: getMaxTokens(type),
        temperature: 0.9,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('[GENERATE-IDEAS] OpenAI API error:', response.status, errorData);
      throw new Error(`OpenAI API error: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;
    
    let ideas = [];
    
    if (type === 'seo-generation') {
      // For SEO generation, return the raw JSON content
      ideas = [content];
    } else {
      // Parse the response to extract ideas
      const sections = content.split(/\n+/).filter(line => line.trim().length > 0);
      
      for (const section of sections.slice(0, count)) {
        const cleanSection = section.replace(/^\d+\.\s*/, '').replace(/^-\s*/, '').trim();
        if (cleanSection) {
          ideas.push(cleanSection);
        }
      }
    }

    console.log(`[GENERATE-IDEAS] Generated ${ideas.length} ideas of type ${type}`);

    return new Response(JSON.stringify({ ideas }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('[GENERATE-IDEAS] Error:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'An unexpected error occurred'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

function getSystemPrompt(type: string): string {
  switch (type) {
    case 'topic-generation':
      return 'You are a creative YouTube content strategist. Generate engaging video topic ideas that would perform well on YouTube. Focus on trending, clickable topics that provide value to viewers.';
    case 'title-generation':
      return 'You are a YouTube title optimization expert. Create compelling, click-worthy titles under 60 characters that drive high CTR while accurately representing the content.';
    case 'hook-generation':
      return 'You are a video hook specialist. Create attention-grabbing opening scripts (first 15 seconds) that immediately capture viewer interest and prevent them from clicking away.';
    case 'thumbnail-ideas':
      return 'You are a YouTube thumbnail designer. Describe compelling thumbnail concepts with specific visual elements, text overlays, and compositions that drive clicks.';
    case 'script-generation':
      return 'You are a YouTube script writer. Create detailed video script outlines with clear structure, engaging content, and smooth transitions that keep viewers watching.';
    case 'description-generation':
      return 'You are a YouTube SEO expert. Write comprehensive video descriptions with relevant keywords, timestamps, and compelling calls-to-action that boost discoverability.';
    case 'seo-generation':
      return 'You are a YouTube SEO specialist. Generate relevant tags and hashtags for maximum video discoverability. Return valid JSON format only.';
    default:
      return 'You are a helpful AI assistant that generates high-quality YouTube content ideas and optimizations.';
  }
}

function getMaxTokens(type: string): number {
  switch (type) {
    case 'script-generation':
      return 800;
    case 'description-generation':
      return 400;
    case 'seo-generation':
      return 300;
    default:
      return 200;
  }
}
