
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.8';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!openAIApiKey) {
      console.error('OpenAI API key not configured.');
      throw new Error('OpenAI API key not configured');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Get user from request
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    
    if (userError || !user) {
      console.error('User authentication failed:', userError);
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    const { title, description, pillar, videoLength, tone } = await req.json();

    if (!title || !videoLength || !tone) {
      throw new Error('Missing required fields: title, videoLength, or tone');
    }

    console.log('[GENERATE-SCRIPT] Processing request for user:', user.id, { title, videoLength, tone });

    // Centralized logging through a dedicated function
    await supabase.functions.invoke('log-ai-usage', {
      body: { user_id: user.id, usage_type: 'script_generation', credits_used: 5 }
    });

    const lengthMap = {
      short: { duration: "60-second", maxTokens: 700, structure: "a 15s hook, 2-3 points (30s), and a 15s CTA." },
      medium: { duration: "5-10 minute", maxTokens: 2000, structure: "a 1-2m intro, 3-4 detailed sections (6-7m), and a 1m conclusion." },
      long: { duration: "10-20 minute", maxTokens: 3500, structure: "a 2-3m intro, 4-5 detailed sections (12-15m), and a 2-3m conclusion." }
    };
    const config = lengthMap[videoLength] || lengthMap.medium;
    const { maxTokens, duration, structure } = config;

    const systemPrompt = `You are a professional YouTube scriptwriter who creates complete, word-for-word video scripts ready for filming.
- NEVER write outlines or summaries.
- Write EVERY SINGLE WORD the host should say.
- Use natural, engaging language with the specified tone.
- Indicate pauses with [PAUSE] and visual cues in [BRACKETS].
- Structure the script with: **HOOK**, **INTRODUCTION**, **MAIN CONTENT**, **CALL TO ACTION**, and **OUTRO**.`;

    const userPrompt = `Create a complete, word-for-word script for a ${duration} YouTube video.

- **Title:** "${title}"
- **Topic:** ${description || 'A general overview.'}
- **Pillar:** ${pillar || 'General'}
- **Tone:** ${tone}
- **Structure:** The script should include ${structure}`;

    console.log('[GENERATE-SCRIPT] Sending detailed request to OpenAI...');

    console.log('[GENERATE-SCRIPT] Sending detailed request to OpenAI...');

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: maxTokens,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('OpenAI API error:', response.status, errorData);
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      console.error('Invalid OpenAI response structure:', data);
      throw new Error('Invalid response from OpenAI API');
    }

    const script = data.choices[0].message.content;

    // Estimate read time (average 150 words per minute for natural speech)
    const wordCount = script.split(' ').length;
    const readTimeMinutes = Math.ceil(wordCount / 150);
    const estimatedReadTime = readTimeMinutes === 1 ? '1 minute' : `${readTimeMinutes} minutes`;

    console.log('[GENERATE-SCRIPT] Complete script generated successfully. Word count:', wordCount);

    return new Response(JSON.stringify({ 
      script,
      estimatedReadTime,
      wordCount 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-script function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
