
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { keyword, userTier } = await req.json();

    if (!keyword) {
      return new Response(JSON.stringify({ error: 'Keyword is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Use GPT-4o for AI Pro users, GPT-3.5-turbo for others
    const model = userTier === 'ai_pro' ? 'gpt-4o' : 'gpt-3.5-turbo';

    const prompt = `As a YouTube keyword strategy expert, analyze the keyword "${keyword}" and provide comprehensive insights.

Return a JSON object with this exact structure:

{
  "keywordIntelligence": {
    "contentType": "Tutorial|Review|Entertainment|Educational|News|Comparison|How-to",
    "competitionLevel": "Low|Medium|High",
    "opportunityScore": 85,
    "bestApproach": "Educational tutorial focusing on beginners"
  },
  "smartKeywordVariations": {
    "low": ["keyword variant 1", "keyword variant 2"],
    "medium": ["keyword variant 3", "keyword variant 4"],
    "high": ["keyword variant 5", "keyword variant 6"]
  },
  "contentStrategy": {
    "titleAngles": ["angle 1", "angle 2", "angle 3"],
    "optimalLength": "8-12 minutes",
    "contentStructure": ["Hook (0-15s)", "Problem intro", "Solution steps", "Examples", "Call to action"],
    "differentiationAdvice": "Focus on practical examples and step-by-step guidance"
  },
  "longTailOpportunities": ["long tail 1", "long tail 2", "long tail 3", "long tail 4", "long tail 5"],
  "proTips": ["tip 1", "tip 2", "tip 3", "tip 4"],
  "competitiveEdge": {
    "missingContent": "Most creators skip the advanced troubleshooting section",
    "uniqueAngle": "Focus on common mistakes and how to avoid them"
  }
}

Guidelines:
- Opportunity score: 0-100 based on search volume vs competition
- Competition levels: Low (easy to rank), Medium (moderate effort), High (very competitive)
- Content structure should be 4-6 actionable steps
- Long-tail opportunities should be specific, searchable phrases
- Pro tips should be actionable and specific to this keyword
- Keep all responses concise but valuable`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages: [
          { role: 'system', content: 'You are a YouTube keyword strategy expert. Always return valid JSON only.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error?.message || 'OpenAI API error');
    }

    const content = data.choices[0].message.content;
    
    // Parse the JSON response
    let analysis;
    try {
      // Clean the response in case it has markdown code blocks
      const cleanContent = content.replace(/```json\n?|\n?```/g, '').trim();
      analysis = JSON.parse(cleanContent);
    } catch (parseError) {
      console.error('Failed to parse AI response:', content);
      throw new Error('Invalid response format from AI');
    }

    return new Response(JSON.stringify(analysis), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in keyword-strategy function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
