// Securely load the client secret from environment variables
const CLIENT_SECRET = Deno.env.get('YOUTUBE_CLIENT_SECRET');
if (!CLIENT_SECRET) {
  console.error('❌ YOUTUBE_CLIENT_SECRET not configured in Supabase secrets');
  throw new Error('YouTube Client Secret not configured. Please set YOUTUBE_CLIENT_SECRET in Supabase secrets.');
}
export { CLIENT_SECRET };

// Ensure we're using the same client ID as the frontend
export const GOOGLE_CLIENT_ID = '343487815633-rtm8h3ptunici9q3f384e2q66clfohu3.apps.googleusercontent.com';

export const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Validate that we have a proper Google client secret format
if (!CLIENT_SECRET.startsWith('GOCSPX-')) {
  console.error('❌ Client secret from environment variable appears to be invalid format. Expected format: GOCSPX-...');
  throw new Error('Invalid YouTube Client Secret format from environment variable. Expected format: GOCSPX-...');
}
